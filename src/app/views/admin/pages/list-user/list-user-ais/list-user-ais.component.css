/* Dashboard Dialog Scrollable Styles */
:host ::ng-deep .dashboard-dialog-container {
  display: flex !important;
  flex-direction: column !important;
  max-height: 90vh !important;
  overflow: hidden !important;
  padding: 10px !important;
}

:host ::ng-deep .dashboard-dialog-container .dx-dialog-content {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  overflow: hidden !important;
  padding: 10px !important;
}

:host ::ng-deep .dashboard-dialog-container .dx-dialog-content > app-dashboard {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* Dashboard content scrollable area */
:host ::ng-deep .dashboard-dialog-container app-dashboard > div {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* Header section - fixed */
:host ::ng-deep .dashboard-dialog-container app-dashboard .lg\\:flex {
  flex-shrink: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
  background: var(--dx-surface-background, #ffffff) !important;
  padding-bottom: 1rem !important;
  border-bottom: 1px solid var(--dx-outline-variant, #e0e0e0) !important;
}

/* Main content area - scrollable */
:host ::ng-deep .dashboard-dialog-container app-dashboard .w-full.flex.flex-col {
  flex: 1 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 10px !important;
  scroll-behavior: smooth !important;
}

/* Custom scrollbar for dashboard content */
:host ::ng-deep .dashboard-dialog-container app-dashboard .w-full.flex.flex-col::-webkit-scrollbar {
  width: 8px;
}

:host ::ng-deep .dashboard-dialog-container app-dashboard .w-full.flex.flex-col::-webkit-scrollbar-track {
  background: transparent;
}

:host ::ng-deep .dashboard-dialog-container app-dashboard .w-full.flex.flex-col::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

:host ::ng-deep .dashboard-dialog-container app-dashboard .w-full.flex.flex-col::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* For Firefox */
:host ::ng-deep .dashboard-dialog-container app-dashboard .w-full.flex.flex-col {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Ensure charts and cards don't break layout */
:host ::ng-deep .dashboard-dialog-container .card-dashboard {
  min-height: auto !important;
  flex-shrink: 0 !important;
}

/* Mobile header adjustments for dialog */
:host ::ng-deep .dashboard-dialog-container app-mobile-header {
  flex-shrink: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 11 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :host ::ng-deep .dashboard-dialog-container {
    max-height: 95vh !important;
  }

  :host ::ng-deep .dashboard-dialog-container app-dashboard .lg\\:flex {
    padding: 0.5rem !important;
  }
}
