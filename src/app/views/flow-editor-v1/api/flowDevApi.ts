import { ChatFlowDev, ChatFlowDevFilter } from '@flow-editor-v1/model';
import { axiosClient } from './axiosClient';

export const flowDevApi = {
  getListFlowDev(body: ChatFlowDevFilter): Promise<Array<ChatFlowDev>> {
    const url = `/v2/flow_dev/list`;
    return axiosClient.post(url, body);
  },

  getFlowDev(id: number): Promise<ChatFlowDev> {
    const url = `/v2/flow_dev/${id}`;
    return axiosClient.get(url);
  },

  saveFlowDev(body: ChatFlowDev): Promise<any> {
    const url = `/v2/flow_dev/save`;
    return axiosClient.post(url, body);
  },

  saveFlowDataDev(body: ChatFlowDev): Promise<any> {
    const url = `/v2/flow_dev/save_data`;
    return axiosClient.post(url, body);
  },
};
