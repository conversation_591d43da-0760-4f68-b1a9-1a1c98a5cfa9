@if (uiStore.isHandset()) {
<app-mobile-header [title]="'Knowledge base'" [backFn]="backFn">
  <div mHeaderRight>
    @let toggleView = toggleViewStyle;
    <ng-container [ngTemplateOutlet]="toggleView"></ng-container>
  </div>
</app-mobile-header>
<div
  class="w-full m-h-full flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
>
  <div class="flex items-center justify-between space-x-3">
    <dx-form-field
      class="w-full flex-1"
      [style.margin-bottom]="0"
      [style.--dx-form-field-label-offset-y]="0"
      [subscriptHidden]="true"
    >
      <app-svg-icon
        dxPrefix
        type="icSearch"
        class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
      <input
        dxInput
        type="text"
        [(ngModel)]="searchModel.name"
        (ngModelChange)="onSearchChange($event)"
        placeholder="Search by Name"
      />
    </dx-form-field>

    <div class="flex-shrink-0 flex items-center space-x-1">
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        cdkOverlayOrigin
        #importMenu="cdkOverlayOrigin"
      >
        <app-svg-icon
          type="icUpload"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="openCreateMenu.set(!openCreateMenu())"
        ></app-svg-icon>
        @let importTemplate = importOverlayTemplate;
        <ng-container
          [ngTemplateOutlet]="importTemplate"
          [ngTemplateOutletContext]="{
            origin: importMenu,
            open: openCreateMenu(),
            onClose: closeMenuExport
          }"
        >
        </ng-container>
      </div>
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        (click)="showCreateFolderDialog()"
      >
        <app-svg-icon
          type="icPlus"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        ></app-svg-icon>
      </div>
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
      >
        <app-svg-icon
          type="icFilter"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="viewFilter.set(true)"
        ></app-svg-icon>
      </div>
    </div>
  </div>
  <div
    class="px-3 m-h-table bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border rounded-[12px]"
  >
    @let contentTemplate = content;
    <ng-container [ngTemplateOutlet]="contentTemplate"></ng-container>
  </div>
</div>
<app-mobile-drawer [visible]="viewFilter()">
  <div class="w-full h-full bg-base-100 dark:bg-dark-base-100">
    <app-mobile-header
      [title]="'Filter'"
      [backFn]="closeFn"
      [hideMenu]="true"
    ></app-mobile-header>
    <div class="w-full h-full flex flex-col p-4 pt-18 gap-y-3">
      <dx-form-field
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
        class="w-full"
      >
        <dx-label>File Type</dx-label>
        <dx-select
          [(ngModel)]="searchModel.file_type"
          (selectionChange)="applyFilter()"
          placeholder="All file type"
        >
          @for (type of listFileType; track type.value) {
          <dx-option [value]="type.value">
            {{ type.label }}
          </dx-option>
          }
        </dx-select>
      </dx-form-field>

      <dx-form-field
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
        class="w-full md:w-48"
      >
        <dx-label>Status</dx-label>
        <dx-select
          [(ngModel)]="searchModel.file_status"
          (selectionChange)="applyFilter()"
          placeholder="All status"
        >
          @for (status of listStatus; track status.value) {
          <dx-option [value]="status.value">
            {{ status.label }}
          </dx-option>
          }
        </dx-select>
      </dx-form-field>
    </div>
  </div>
</app-mobile-drawer>
} @else {
<div class="flex flex-col overflow-hidden h-[calc(100dvh-48px)] gap-6">
  <div class="flex flex-col items-stretch justify-start">
    <div class="flex flex-wrap space-x-3 hidden xl:flex">
      <div
        class="max-w-[300px] text-[28px] font-bold text-base-content dark:text-dark-base-content truncate flex items-center hover:cursor-pointer hover:!text-neutral-content dark:hover:!text-dark-neutral-content"
        (click)="toBreadcrumbItem(undefined)"
      >
        Knowledge Base
      </div>
      @for (breadcrumb of breadcrumbs(); track breadcrumb) {
      <div
        class="max-w-[300px] text-2xl font-semibold !text-neutral-content dark:!text-dark-neutral-content truncate text-left flex items-center hover:cursor-pointer hover:opacity-80"
        [ngClass]="{
          '!text-base-content dark:!text-dark-base-content':
            breadcrumb.id === parentId()
        }"
        (click)="toBreadcrumbItem(breadcrumb)"
      >
        <app-svg-icon type="icChevronArrow" class="w-6 h-6"></app-svg-icon>
        &ensp;{{ breadcrumb.name }}
      </div>
      }
    </div>

    <div
      class="w-full flex flex-wrap items-center justify-start space-x-3 block xl:hidden bg-base-200 dark:bg-dark-base-200 px-4 py-3"
    >
      <div
        class="text-base-content dark:text-dark-base-content text-2xl truncate flex items-center hover:cursor-pointer hover:!text-neutral-content hover:dark:!text-dark-neutral-content"
        [ngClass]="{ '!text-neutral-content': breadcrumbs().length > 0 }"
        (click)="toBreadcrumbItem(undefined)"
      >
        Knowledge Base
      </div>
      @if (breadcrumbs().length) {
      <div
        class="max-w-[300px] text-[#94a2b8] text-2xl truncate text-left flex items-center hover:cursor-pointer hover:!text-light-text dark:text-dark-text"
        (click)="toBreadcrumbItem(breadcrumbs()[breadcrumbs().length - 1])"
      >
        <app-svg-icon
          type="icChevronArrow"
          class="w-5 h-5 !text-[#94a2b8]"
        ></app-svg-icon
        >&ensp;{{ breadcrumbs()[breadcrumbs().length - 1].name }}
      </div>
      }
    </div>
  </div>

  <div
    class="space-y-6 p-6 h-full rounded-2xl flex flex-col overflow-hidden responsive-height bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
  >
    @if (showSelectionInfo()) {
    <div class="knowledge-bar box-border mb-6">
      <div
        class="flex items-center justify-between w-full px-4 py-[3px] rounded-full bg-base-100 dark:bg-dark-base-100 text-base-content dark:text-dark-base-content border border-primary-border dark:border-dark-primary-border"
      >
        <div class="flex items-center">
          <app-svg-icon
            type="icFolderFilled"
            class="w-5 h-5 mr-2"
          ></app-svg-icon>
          <span class="font-medium">{{ selectionText() }}</span>
        </div>
        <div class="flex items-center">
          @if (folderSelection.selected && folderSelection.selected.length &&
          fileSelection.selected.length === 0 && isAllowAssign) {
          <div
            (click)="openAssignPermissionDialog()"
            (mouseup)="$event.stopPropagation()"
            class="ml-6 flex items-center justify-center w-8 h-8 rounded-full aspect-square hover:cursor-pointer hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
          >
            <app-svg-icon
              type="icPlus"
              class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
          </div>
          } @if (parentId()) {
          <div
            (click)="deleteAll()"
            (mouseup)="$event.stopPropagation()"
            class="ml-2 flex items-center justify-center w-8 h-8 rounded-full aspect-square hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-dark-hover"
          >
            <app-svg-icon
              type="icTrash"
              class="w-6 h-6 !text-error dark:!text-dark-error"
            ></app-svg-icon>
          </div>
          } @if (!parentId()) { @if (isAllowAssign) {
          <div
            (click)="deleteAll()"
            (mouseup)="$event.stopPropagation()"
            class="ml-2 flex items-center justify-center w-8 h-8 rounded-full aspect-square hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-dark-hover"
          >
            <app-svg-icon
              type="icTrash"
              class="w-6 h-6 !text-error dark:!text-dark-error"
            ></app-svg-icon>
          </div>
          } }
          <button
            (click)="clearAllSelectedFolderAndFile()"
            class="ml-2 flex items-center justify-center w-8 h-8 rounded-full aspect-square hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-dark-hover"
          >
            <app-svg-icon
              type="icClose"
              class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
          </button>
        </div>
      </div>
    </div>
    } @else {
    <div class="flex flex-wrap items-center justify-between gap-y-3">
      <div class="flex flex-wrap items-center gap-x-3 gap-y-2">
        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full md:w-64"
        >
          <app-svg-icon
            dxPrefix
            type="icSearch"
            class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
          ></app-svg-icon>
          <input
            dxInput
            type="text"
            [(ngModel)]="searchModel.name"
            (ngModelChange)="onSearchChange($event)"
            placeholder="Search by Name"
          />
        </dx-form-field>

        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full md:w-48"
        >
          <dx-select
            [(ngModel)]="searchModel.file_type"
            (selectionChange)="applyFilter()"
            placeholder="All file type"
          >
            @for (type of listFileType; track type.value) {
            <dx-option [value]="type.value">
              {{ type.label }}
            </dx-option>
            }
          </dx-select>
        </dx-form-field>

        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full md:w-48"
        >
          <dx-select
            [(ngModel)]="searchModel.file_status"
            (selectionChange)="applyFilter()"
            placeholder="All status"
          >
            @for (status of listStatus; track status.value) {
            <dx-option [value]="status.value">
              {{ status.label }}
            </dx-option>
            }
          </dx-select>
        </dx-form-field>
      </div>

      <div class="flex flex-wrap items-center gap-x-3 gap-y-2">
        @let toggleView2 = toggleViewStyle;
        <ng-container [ngTemplateOutlet]="toggleView2"></ng-container>
        <div
          class="text-xs font-bold text-primary-border dark:text-dark-primary-border"
        >
          |
        </div>

        <div cdkOverlayOrigin #importMenu="cdkOverlayOrigin">
          <button
            dxButton="elevated"
            (click)="openCreateMenu.set(!openCreateMenu())"
          >
            <div
              class="flex items-center gap-x-2 !text-neutral-content dark:!text-dark-neutral-content"
            >
              <app-svg-icon type="icUpload" class="w-6 h-6"></app-svg-icon>
              <p class="text-sm font-normal">Import</p>
            </div>
          </button>
          @let importTemplate2 = importOverlayTemplate;
          <ng-container
            [ngTemplateOutlet]="importTemplate2"
            [ngTemplateOutletContext]="{
              origin: importMenu,
              open: openCreateMenu(),
              onClose: closeMenuExport
            }"
          >
          </ng-container>
        </div>
        <button dx-button="filled" (click)="showCreateFolderDialog()">
          <div class="flex items-center justify-between space-x-1">
            <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
            <span class="text-sm">Create folder</span>
          </div>
        </button>
      </div>
    </div>
    } @let contentTemplate2 = content;
    <ng-container [ngTemplateOutlet]="contentTemplate2"></ng-container>
  </div>
</div>
}

<ng-template #assignPermissionFolderDialog let-data>
  <div class="text-light-text dark:text-dark-text h-full">
    <div
      class="header border-bottom-gray pt-6 px-6 flex justify-between items-center pb-3"
    >
      <div class="text-2xl font-bold card-title capitalize">
        Share {{ data.countFolder }}
        {{ data.countFolder > 1 ? "folders" : "folder" }}
      </div>
      <app-svg-icon
        type="icClose"
        class="justify-self-end w-6 h-6 hover:cursor-pointer text-neutral-content dark:text-dark-neutral-content"
        (click)="closeDxDialog()"
      ></app-svg-icon>
    </div>

    <div
      class="w-full px-6 content overflow-auto max-h-[75vh]"
      [ngClass]="folderSelection.selected.length == 1 ? 'pt-6' : ''"
    >
      @if (!(folderSelection.selected.length > 1)) {
      <dx-select
        class="block my-2"
        [value]="getAssignPermissionsFormArrayValue('user_id')"
        (valueChange)="addAssignPermissions($event)"
        placeholder="Select user"
      >
        @for (user of listUserInAIAllowed(); track user.id) {
        <dx-option [value]="user.id">{{ user.email }}</dx-option>
        }
      </dx-select>
      }
      <form [formGroup]="assignFolderForm" class="mt-6">
        <h1 class="mb-3">Users with access</h1>
        @for (assignPermission of assignPermissionsFormArray.controls; track
        assignPermission; let i = $index) {
        <div class="w-full mb-4 grid grid-cols-8 gap-5 items-center h-12">
          <div class="col-span-4 h-full flex items-center space-x-3">
            <div
              class="w-10 aspect-square rounded-full bg-[#00BFFF] flex items-center justify-center"
            >
              {{ assignPermission.get("username")?.value | charFirst }}
            </div>
            <div class="flex flex-col space-y-1">
              <div class="text-light-text dark:text-dark-text truncate">
                {{ assignPermission.get("username")?.value || "" }}
              </div>
              @if (assignPermission.get('folder_id')?.value) {
              <div class="flex flex-col items-stretch">
                <div class="flex items-center space-x-2">
                  <div class="flex items-center justify-center">
                    <app-svg-icon
                      type="icFolderFilled"
                      class="w-4 h-4 text-gray-500"
                    ></app-svg-icon>
                  </div>
                  <div
                    class="w-[calc(100%-26px)] overflow-hidden text-left flex flex-col"
                  >
                    <div class="truncate text-gray-500 text-sm">
                      {{
                        getFolderById(
                          assignPermission.get("folder_id")?.value || 0
                        )?.name || ""
                      }}
                    </div>
                  </div>
                </div>
              </div>
              }
            </div>
          </div>
          <div class="col-span-4 h-full w-full flex items-center">
            <dx-select
              class="w-full block !text-[12px]"
              [value]="
                getAssignPermissionsFormArrayValueIndex('permissions', i)
              "
              (valueChange)="selectPermissions($event, i)"
              placeholder="Select permissions"
              [multiple]="true"
            >
              @for (permission of listPermissions; track permission.value) {
              <dx-option [value]="permission.value">{{
                permission.label
              }}</dx-option>
              }
            </dx-select>
          </div>
        </div>
        }
      </form>
    </div>

    @if (!(folderSelection.selected.length > 1)) {
    <div class="footer p-6 flex justify-end">
      <div class="flex">
        <button
          id="btn-close-dialog-assign-permissions-2"
          class="cursor-pointer h-[40px] min-w-[120px] px-3 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background border-light-gray"
          (click)="closeDxDialog()"
        >
          <span>Cancel</span>
        </button>
        <button
          dx-button="filled"
          id="btn-submit-intent-form"
          class="h-[40px] min-w-[120px] bg-light-user-chat-background dark:bg-dark-user-chat-background text-white px-3 rounded-2xl border-light-gray button-disabled hover:cursor-pointer ml-3"
          [disabled]="assignFolderForm.invalid"
          (click)="savePermissionAssign()"
        >
          Save
        </button>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #toggleViewStyle>
  <div
    class="p-1 rounded-xl cursor-pointer bg-base-300 dark:bg-dark-base-300 border border-primary-border dark:border-dark-primary-border"
  >
    <div
      class="flex flex-row overflow-hidden text-neutral-content dark:text-dark-neutral-content bg-transparent"
    >
      <div
        class="flex items-center justify-center rounded-lg p-1"
        [ngClass]="{
          'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs inset-shadow-decoration-100 shadow-md':
            showStyle() === 'list'
        }"
        (click)="toggleView('list')"
      >
        <app-svg-icon
          type="icList"
          class="w-5.5 h-5.5"
          [ngClass]="[
            showStyle() === 'list'
              ? '!text-base-content dark:!text-dark-base-content'
              : '!text-neutral-content dark:!text-dark-neutral-content'
          ]"
        ></app-svg-icon>
      </div>
      <div
        class="flex items-center justify-center rounded-lg p-1"
        [ngClass]="{
          'bg-base-400 dark:bg-dark-base-400 inset-shadow-xs dark:inset-shadow-dark-decoration-100 shadow-md':
            showStyle() === 'grid'
        }"
        (click)="toggleView('grid')"
      >
        <app-svg-icon
          type="icCategory"
          class="w-5.5 h-5.5"
          [ngClass]="[
            showStyle() === 'grid'
              ? '!text-base-content dark:!text-dark-base-content '
              : '!text-neutral-content dark:!text-dark-neutral-content'
          ]"
        ></app-svg-icon>
      </div>
    </div>
  </div>
</ng-template>

<ng-template
  #importOverlayTemplate
  let-origin="origin"
  let-open="open"
  let-onClose="onClose"
>
  <ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="origin"
    [cdkConnectedOverlayOpen]="open"
    [cdkConnectedOverlayPositions]="[
      {
        originX: 'start',
        originY: 'bottom',
        overlayX: 'center',
        overlayY: 'top',
        offsetY: 5
      }
    ]"
  >
    <ul
      (clickOutside)="onClose()"
      class="p-2 w-[245px] flex flex-col justify-between shadow-lg rounded-xl border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
    >
      <li
        (click)="showImportUrlDialog()"
        class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
      >
        <div
          class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
        >
          <app-svg-icon type="icLinkPrefix" class="w-6 h-6 mr-3"></app-svg-icon>
          Import URL
        </div>
      </li>
      <li
        (click)="showUploadDialog()"
        class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
      >
        <div
          class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
        >
          <app-svg-icon type="icUpload" class="w-6 h-6 mr-3"></app-svg-icon>
          Import File
        </div>
      </li>
    </ul>
  </ng-template>
</ng-template>

<ng-template #content>
  <div
    class="flex-grow overflow-hidden"
    [ngStyle]="{
      height: showStyle() === 'grid' ? 'calc(100% - 50px)' : 'calc(100% - 0px)'
    }"
  >
    @if (searchingMode()) {
    <div class="p-4">
      <h1 class="text-light-text dark:text-dark-text text-xl">Search result</h1>
    </div>
    } @if (isLoadingMore() && folderList().length === 0 && fileList().length ===
    0) {
    <div class="w-full m-h-full flex items-center justify-center p-4">
      <dx-progress-spinner [diameter]="48"></dx-progress-spinner>
    </div>
    } @if (folderList().length > 0 || fileList().length > 0) { @switch
    (showStyle()) { @case ('grid') {
    <app-grid-view
      [fileList]="fileList()"
      [fileSelection]="fileSelection"
      [folderList]="folderList()"
      [folderSelection]="folderSelection"
      [searchModel]="searchModel"
      [isShiftPressed]="isShiftPressed"
      [isCtrlPressed]="isCtrlPressed"
      (fileContextMenu)="onFileContextMenu($event)"
      (fileDelete)="onFileDelete($event)"
      (fileDoubleClick)="!uiStore.isHandset() && onFileDoubleClick($event)"
      (fileInfo)="onFileInfo($event)"
      (fileMove)="onFileMove($event)"
      (fileRename)="onFileRename($event)"
      (fileSelected)="!uiStore.isHandset() && onFileSelected($event)"
      (folderContextMenu)="onFolderContextMenu($event)"
      (folderDelete)="onFolderDelete($event)"
      (folderDoubleClick)="!uiStore.isHandset() && onFolderDoubleClick($event)"
      (folderRename)="onFolderRename($event)"
      (folderSelected)="
        uiStore.isHandset()
          ? onFolderDoubleClick($event.folder || $event)
          : onFolderSelected($event)
      "
      (refreshRequested)="onRefreshRequested()"
    >
    </app-grid-view>
    } @case ('list') {
    <app-list-view
      [combinedList]="combinedList()"
      [fileSelection]="fileSelection"
      [folderSelection]="folderSelection"
      [searchModel]="searchModel"
      [isShiftPressed]="isShiftPressed"
      [isCtrlPressed]="isCtrlPressed"
      (fileContextMenu)="onFileContextMenu($event)"
      (fileDelete)="onFileDelete($event)"
      (fileDoubleClick)="!uiStore.isHandset() && onFileDoubleClick($event)"
      (fileInfo)="onFileInfo($event)"
      (fileMove)="onFileMove($event)"
      (fileRename)="onFileRename($event)"
      (fileSelected)="!uiStore.isHandset() && onFileSelected($event)"
      (folderContextMenu)="onFolderContextMenu($event)"
      (folderDelete)="onFolderDelete($event)"
      (folderDoubleClick)="!uiStore.isHandset() && onFolderDoubleClick($event)"
      (folderRename)="onFolderRename($event)"
      (folderSelected)="
        uiStore.isHandset()
          ? onFolderDoubleClick($event.folder || $event)
          : onFolderSelected($event)
      "
      (sortChanged)="onSortChanged($event)"
    >
    </app-list-view>
    } } } @else { @if (!isLoadingMore()) {
    <div
      class="flex flex-col items-center justify-center h-full text-center p-8"
    >
      <app-svg-icon
        type="icFolderFilled"
        class="w-15 h-15 text-light-text dark:text-dark-text opacity-50 mb-4"
      ></app-svg-icon>
      <h3 class="text-xl text-light-text dark:text-dark-text mb-2">
        No files or folders found
      </h3>
      <p class="text-light-text dark:text-dark-text opacity-70">
        Upload some files or create folders to get started
      </p>
    </div>
    } } @if (hasMoreData() && !isLoadingMore() && (folderList().length > 0 ||
    fileList().length > 0)) {
    <div class="flex justify-center p-4">
      <button
        class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center space-x-2"
        (click)="loadMore()"
      >
        <span>Load More</span>
      </button>
    </div>
    } @if (isLoadingMore() && (folderList().length > 0 || fileList().length >
    0)) {
    <div class="flex justify-center p-4">
      <div
        class="flex items-center space-x-2 text-light-text dark:text-dark-text"
      >
        <div
          class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"
        ></div>
        <span>Loading more...</span>
      </div>
    </div>
    }
  </div>
</ng-template>
