<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="text-2xl text-base-content dark:text-dark-base-content font-bold"
    >
      AI list
    </div>
    <ng-icon
      name="heroXMark"
      class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
      (click)="dialogRef.close()"
    ></ng-icon>
  </div>

  <div class="flex-1 mt-18 mb-20 flex flex-col p-6 overflow-y-auto">
    <dx-form-field class="w-full" id="search">
      <dx-label class="text-sm">Search</dx-label>
      <input
        dxInput
        [(ngModel)]="filterAI.name"
        (ngModelChange)="filterAIList()"
        [type]="'text'"
        placeholder="Search by Name"
      />
    </dx-form-field>
    <app-data-table
      class="min-h-[450px]"
      [rows]="listAI()"
      [columns]="columnsAI"
      [rowTemplate]="rowTemplate"
      [hiddenPaginator]="true"
      [loading]="isLoading()"
    >
      <ng-template #rowTemplate let-row="row" let-column="column">
        <ng-container [ngSwitch]="column.columnDef">
          <ng-container *ngSwitchCase="'created_at'">
            <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
              {{ row[column.columnDef] | date : "dd/MM/yyyy HH:mm:ss" }}
            </div>
          </ng-container>
          <ng-container *ngSwitchCase="'action_edit'">
            <div
              class="flex justify-center items-center"
              [ngStyle]="{ 'justify-content': column.align }"
            >
              <app-svg-icon
                type="icMoreHorizontal"
                class="w-6 h-6 flex items-center justify-center cursor-pointer hover:opacity-80"
                (click)="row.isActions = !row.isActions"
                cdkOverlayOrigin
                #trigger="cdkOverlayOrigin"
              ></app-svg-icon>

              <ng-template
                cdkConnectedOverlay
                [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="row.isActions && !row.isContextMenu"
                [cdkConnectedOverlayPush]="true"
                [cdkConnectedOverlayPositions]="[
                  { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'top', offsetY: 10 },
                  { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'bottom', offsetY: 10 }
                ]"
              >
                <ul
                  class="w-[200px] p-2 rounded-xl !text-base-content dark:!text-dark-base-content shadow-md flex flex-col gap-y-1 action-dropdown border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                  (clickOutside)="row.isActions = false"
                >
                  <li class="w-full">
                    <div
                      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                      (click)="showDashboard(row); row.isActions = false"
                    >
                      <ng-icon
                        name="heroEye"
                        class="text-xl !text-neutral-content dark:!text-dark-neutral-content"
                      ></ng-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        View Dashboard
                      </div>
                    </div>
                  </li>
                  <li class="w-full">
                    <div
                      class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                      (click)="editAI(row); row.isActions = false"
                    >
                      <ng-icon
                        name="heroPencil"
                        class="text-xl !text-neutral-content dark:!text-dark-neutral-content"
                      ></ng-icon>
                      <div class="flex items-center justify-between text-[16px] font-medium">
                        Edit
                      </div>
                    </div>
                  </li>
                </ul>
              </ng-template>
            </div>
          </ng-container>
          <ng-container *ngSwitchDefault>
            <div class="flex" [ngStyle]="{ 'justify-content': column.align }">
              {{ row[column.columnDef] }}
            </div>
          </ng-container>
        </ng-container>
      </ng-template>
    </app-data-table>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
  </div>
</div>
