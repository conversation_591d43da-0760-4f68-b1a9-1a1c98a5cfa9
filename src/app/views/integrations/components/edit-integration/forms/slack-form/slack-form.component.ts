import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  effect,
  inject,
  signal,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DxForm<PERSON>ield, DxInput, DxLabel, DxSnackBar } from '@dx-ui/ui';
import { NgIconsModule } from '@ng-icons/core';

@Component({
  selector: 'app-slack-form',
  standalone: true,
  imports: [ReactiveFormsModule, DxInput, DxFormField, DxLabel, NgIconsModule],
  templateUrl: './slack-form.component.html',
})
export class SlackFormComponent implements OnInit {
  @Input() initialData!: any;
  @Input() webhookUrl!: string;
  @Output() formChange = new EventEmitter<FormGroup>();

  fb = inject(FormBuilder);
  private snackBar = inject(DxSnackBar);

  formGroup = this.fb.group({ slack_token: ['', Validators.required] });
  linkWebHook = signal('');

  constructor() {
    effect(() => {
      this.formChange.emit(this.formGroup);
    });
  }

  ngOnInit() {
    if (this.initialData) this.formGroup.patchValue(this.initialData);
    this.linkWebHook.set(this.webhookUrl);
  }

  async copyText(content: string) {
    try {
      await navigator.clipboard.writeText(content);
      this.snackBar.open('Copied successfully', '', {
        panelClass: 'dx-snack-bar-success',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    } catch {
      this.snackBar.open('Copy failed', '', {
        panelClass: 'dx-snack-bar-error',
        duration: 5000,
        verticalPosition: 'top',
        horizontalPosition: 'right',
      });
    }
  }
}
