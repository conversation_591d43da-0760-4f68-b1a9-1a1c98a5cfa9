import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  OnInit,
  QueryList,
  ViewChildren,
  computed,
  inject,
  input,
  output,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { UIStore } from '@core/stores';
import { DxButton, DxFormField, DxInput, DxSuffix, DxTooltip } from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroEllipsisHorizontal,
  heroHandRaised,
  heroPlay,
} from '@ng-icons/heroicons/outline';
import { TYPE_INTEGRATION } from '@shared/app.constant';
import { BaseComponent, SvgIconComponent } from '@shared/components';
import { ClickOutsideDirective } from '@shared/directives';
import { IConversation } from '@shared/models';
import { MessagesService } from '@shared/services';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';

@Component({
  selector: 'app-conversation-list',
  templateUrl: './conversation-list.component.html',
  styleUrls: ['./conversation-list.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    InfiniteScrollDirective,
    MatProgressSpinnerModule,
    MatTooltipModule,
    NgIconsModule,
    SvgIconComponent,
    DxTooltip,
    DxButton,
    DxFormField,
    DxInput,
    DxSuffix,
    ClickOutsideDirective,
    NgIconsModule,
    OverlayModule,
  ],
  providers: [
    provideIcons({ heroEllipsisHorizontal, heroPlay, heroHandRaised }),
  ],
})
export class ConversationListComponent extends BaseComponent implements OnInit {
  @ViewChildren('conversationItem') conversationItems!: QueryList<ElementRef>;

  // Input signals
  conversations = input<IConversation[]>([]);
  selectedConversationId = input<string>('');
  isLoadingConversation = input<boolean>(false);
  isChatListActive = input<boolean>(true);
  nameConversationNew = input<string>('');
  listTagSelectedInConversation = input<any[]>([]);
  shouldPaginate = input<boolean>(false);
  openExportOptions = input<boolean>(false);
  searchContent = input<string>('');
  searchName = input<string>('');
  timeFrom = input<Date | null>(null);
  timeTo = input<Date | null>(null);
  hasMoreData = input<boolean>(true);
  totalConversations = input<number>(0);

  // Output signals
  conversationSelected = output<string>();
  mConversationSelectedViewed = output<boolean>();
  scrolled = output<void>();
  nameConversationNewChange = output<string>();
  conversationsUpdated = output<IConversation[]>();
  conversationStatusChanged = output<{
    conversationId: string;
    action: 'takeOver' | 'resume';
    isChatWithAI: boolean;
  }>();

  // Internal state signals
  viewMenuConvItem = signal<string>('');
  private editingConversationId = signal<string>('');
  private isProcessingAction = signal(false);

  // Computed signals
  hasConversations = computed(() => this.conversations().length > 0);

  selectedConversation = computed(() =>
    this.conversations().find((c) => c.id === this.selectedConversationId())
  );

  filteredConversations = computed(() => {
    const conversations = this.conversations();
    const searchName = this.searchName();

    if (!searchName) {
      return conversations;
    }

    return conversations.filter((conversation) => {
      return conversation.name
        ?.toLowerCase()
        .includes(searchName.toLowerCase());
    });
  });

  canScroll = computed(
    () =>
      this.hasMoreData() &&
      !this.isLoadingConversation() &&
      this.shouldPaginate()
  );

  showEmptyState = computed(
    () => !this.hasConversations() && !this.isLoadingConversation()
  );

  showLoadingState = computed(
    () => this.isLoadingConversation() && !this.hasConversations()
  );

  // Constants
  TYPE_INTEGRATION = TYPE_INTEGRATION;

  uiStore = inject(UIStore);
  private messagesService = inject(MessagesService);

  ngOnInit(): void {
    // Component initialization logic
  }

  onSelectConversation(conversationId: string): void {
    if (conversationId === this.selectedConversationId()) return;
    // Tự động tắt has_unread_message khi click vào conversation
    const conversations = this.conversations();
    const selectedConversation = conversations.find(
      (c) => c.id === conversationId
    );

    if (selectedConversation && selectedConversation.has_unread_message) {
      const updatedConversations = conversations.map((c) =>
        c.id === conversationId ? { ...c, has_unread_message: false } : c
      );
      this.conversationsUpdated.emit(updatedConversations);
    }

    this.conversationSelected.emit(conversationId);
  }

  onScroll(): void {
    if (this.canScroll()) {
      this.scrolled.emit();
    }
  }

  onEditNameConversation(event: any, conversationId: string): void {
    event.stopPropagation();

    // Close any other editing conversations
    this.closeAllEditingConversations();

    const conversation = this.conversations().find(
      (c) => c.id === conversationId
    );
    if (conversation) {
      this.editingConversationId.set(conversationId);
      const updatedConversations = this.conversations().map((c) =>
        c.id === conversationId
          ? { ...c, isEditName: true }
          : { ...c, isEditName: false }
      );
      this.conversationsUpdated.emit(updatedConversations);
      this.nameConversationNewChange.emit(conversation.name || '');
    }
  }

  onSaveEditNameConversation(event: any, conversationId: string): void {
    event.stopPropagation();

    if (this.isProcessingAction()) return;

    const conversation = this.conversations().find(
      (c) => c.id === conversationId
    );
    const newName = this.nameConversationNew();

    if (!conversation || !newName.trim()) {
      this.onCloseEditNameConversation(event, conversationId);
      return;
    }

    this.isProcessingAction.set(true);

    const body = {
      id: conversationId,
      name: newName.trim(),
    };

    this.messagesService.renameConversation(body).subscribe({
      next: (res: any) => {
        const updatedConversations = this.conversations().map((c) =>
          c.id === conversationId
            ? { ...c, name: newName.trim(), isEditName: false }
            : c
        );
        this.conversationsUpdated.emit(updatedConversations);
        this.editingConversationId.set('');
        this.showSnackBar('Conversation name updated successfully', 'success');
        this.isProcessingAction.set(false);
      },
      error: (err: any) => {
        this.showSnackBar('Failed to update conversation name', 'error');
        this.isProcessingAction.set(false);
      },
    });
  }

  onCloseEditNameConversation(event: any, conversationId: string): void {
    this.closeEditingConversation(conversationId);
  }

  onTakeOverConversation(event: any, conversationId: string): void {
    event.stopPropagation();

    if (this.isProcessingAction()) return;

    // Close all editing conversations
    this.closeAllEditingConversations();
    this.isProcessingAction.set(true);

    const params = {
      conversation_id: conversationId,
    };

    this.messagesService.takeOverConversation(params).subscribe({
      next: (res: any) => {
        this.showSnackBar('Success take over the conversation', 'success');

        const updatedConversations = this.conversations().map((c) =>
          c.id === conversationId
            ? { ...c, ...res, isEditName: false }
            : { ...c, isEditName: false }
        );
        this.conversationsUpdated.emit(updatedConversations);

        // Determine isChatWithAI status
        const isChatWithAI = Boolean(
          res.assigned && res.assigned !== 'Unassigned'
        );

        // Emit conversation status change
        this.conversationStatusChanged.emit({
          conversationId,
          action: 'takeOver',
          isChatWithAI,
        });

        this.isProcessingAction.set(false);
      },
      error: (err: any) => {
        this.showSnackBar('Failed to take over conversation', 'error');
        this.isProcessingAction.set(false);
      },
    });
  }

  onResumeConversation(event: any, conversationId: string): void {
    event.stopPropagation();

    if (this.isProcessingAction()) return;

    // Close all editing conversations
    this.closeAllEditingConversations();
    this.isProcessingAction.set(true);

    const params = {
      conversation_id: conversationId,
    };

    this.messagesService.returnToAIConversation(params).subscribe({
      next: (res: any) => {
        this.showSnackBar('Success return to AI the conversation', 'success');

        const updatedConversations = this.conversations().map((c) =>
          c.id === conversationId
            ? { ...c, ...res, isEditName: false }
            : { ...c, isEditName: false }
        );
        this.conversationsUpdated.emit(updatedConversations);

        // Determine isChatWithAI status
        const isChatWithAI = Boolean(
          res.assigned && res.assigned !== 'Unassigned'
        );

        // Emit conversation status change
        this.conversationStatusChanged.emit({
          conversationId,
          action: 'resume',
          isChatWithAI,
        });

        this.isProcessingAction.set(false);
      },
      error: (err: any) => {
        this.showSnackBar('Failed to resume conversation', 'error');
        this.isProcessingAction.set(false);
      },
    });
  }

  onNameConversationNewChange(value: string): void {
    this.nameConversationNewChange.emit(value);
  }

  onNameInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.nameConversationNewChange.emit(target.value);
  }

  // Helper methods
  private closeEditingConversation(conversationId: string): void {
    const updatedConversations = this.conversations().map((c) =>
      c.id === conversationId ? { ...c, isEditName: false } : c
    );
    this.conversationsUpdated.emit(updatedConversations);
    this.editingConversationId.set('');
    this.nameConversationNewChange.emit('');
  }

  private closeAllEditingConversations(): void {
    const updatedConversations = this.conversations().map((c) => ({
      ...c,
      isEditName: false,
    }));
    this.conversationsUpdated.emit(updatedConversations);
    this.editingConversationId.set('');
    this.nameConversationNewChange.emit('');
  }

  // Check if conversation is being edited
  isEditingConversation(conversationId: string): boolean {
    return this.editingConversationId() === conversationId;
  }

  // Check if any action is in progress
  isActionInProgress(): boolean {
    return this.isProcessingAction();
  }

  trackByFn(_index: number, row: any): number {
    return row.id;
  }

  getColor(config: string): string {
    try {
      const parsedConfig = JSON.parse(config);
      return parsedConfig.color || '#000';
    } catch (error) {
      console.error('Invalid config:', config);
      return '#000'; // Default color nếu lỗi
    }
  }

  getTextColor(bgColor: string): string {
    const hexToRgb = (hex: string) => ({
      r: parseInt(hex.substring(0, 2), 16),
      g: parseInt(hex.substring(2, 4), 16),
      b: parseInt(hex.substring(4, 6), 16),
    });

    const calculateTextColor = ({
      r,
      g,
      b,
    }: {
      r: number;
      g: number;
      b: number;
    }): string => {
      const adjustComponent = (comp: number) => Math.floor(comp * 0.4);

      const textRgb = {
        r: adjustComponent(r),
        g: adjustComponent(g),
        b: adjustComponent(b),
      };

      return `#${textRgb.r.toString(16).padStart(2, '0')}${textRgb.g
        .toString(16)
        .padStart(2, '0')}${textRgb.b.toString(16).padStart(2, '0')}`;
    };

    const rgb = hexToRgb(bgColor.replace('#', ''));
    return calculateTextColor(rgb);
  }

  scrollToConversation(conversationId: string): void {
    const conversationIndex = this.conversations().findIndex(
      (c) => c.id === conversationId
    );
    if (conversationIndex !== -1 && this.conversationItems) {
      const element =
        this.conversationItems.toArray()[conversationIndex]?.nativeElement;
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  }

  mViewDetail() {
    this.mConversationSelectedViewed.emit(true);
  }
}
