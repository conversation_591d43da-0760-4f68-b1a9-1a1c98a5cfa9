@if (uiStore.isHandset()) {
<app-mobile-header [title]="title" [hideBack]="true">
  <div mHeaderRight class="flex items-center">
    @if (isReloadChat()){
    <dx-progress-spinner [diameter]="24"></dx-progress-spinner>
    } @else {
    <app-svg-icon
      type="icSync"
      class="w-6 h-6 mr-2 cursor-pointer !text-neutral-content dark:!text-dark-neutral-content"
      (click)="reloadChat(true)"
    ></app-svg-icon>
    }
  </div>
</app-mobile-header>
<div
  class="w-full h-full-nav relative flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden bg-base-100 dark:bg-dark-base-100"
>
  <div class="h-full flex-1 flex flex-grow flex-col overflow-hidden">
    <div class="overflow-y-auto flex-1">
      <ng-scrollbar
        class="scroll-custom"
        #scrollRef
        track="vertical"
        appearance="compact"
        visibility="hover"
        (vmScrolled)="onScroll()"
      >
        <div class="space-y-4 mb-16">
          @for (chat of listChatMessage(); track trackByChat($index, chat); let
          last = $last) {
          <div
            class="flex items-start"
            [ngClass]="{
              'justify-start':
                chat.role === 'assistant' || chat.role === 'human_operator',
              'justify-end': chat.role === 'user'
            }"
          >
            @if ((chat.role === 'assistant' || chat.role === 'human_operator')
            && chat.typeMessage !== 'error' && chat.typeMessage !== 'logging' &&
            chat.content) {
            <div>
              <div class="w-7 h-7 rounded-full relative">
                <img
                  src="./assets/img/logo-chat.png"
                  alt="This is logo"
                  class="w-7 h-7"
                />
              </div>
            </div>
            }

            <div
              class="flex items-center max-w-[80%]"
              [class.w-full]="
                isEditingMessage() &&
                editingMessageId() === chat.id &&
                chat.role == 'user'
              "
            >
              @if (!isEditingMessage() && !isSendingMessage() && chat.role ==
              'user' && chat.typeMessage !== 'complex') {
              <ng-icon
                name="heroPencilSquareMini"
                size="20"
                class="flex-shrink-0 !text-yellow-500 cursor-pointer"
                (click)="editMessage(chat)"
                dxTooltip="Edit message"
                tooltipPosition="bottom"
              ></ng-icon>
              } @if (isEditingMessage() && editingMessageId() === chat.id &&
              chat.role == 'user' && chat.typeMessage !== 'complex') {
              <div
                class="w-full p-2.5 flex flex-col space-y-3 bg-gray-200 dark:bg-dark-secondary rounded-xl"
              >
                <textarea
                  class="w-full p-2 rounded-lg placeholder-gray-400 dark:placeholder-gray-200 text-sm border border-gray-300 rounded-lg focus:outline-none resize-none bg-gray-50 dark:bg-dark-background text-light-text dark:text-dark-text focus:ring-light-primary focus:border-light-primary dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-light-primary dark:focus:border-light-primary"
                  [(ngModel)]="editedMessage"
                  [ngModelOptions]="{ standalone: true }"
                  rows="2"
                  (input)="adjustHeight($event)"
                >
                </textarea>
                <div class="w-full flex justify-end items-center space-x-3">
                  <button
                    class="bg-gray-500 px-3 py-1.5 rounded-full text-white text-sm"
                    (click)="cancelEditMessage(chat)"
                  >
                    Cancel
                  </button>
                  <button
                    class="bg-light-primary dark:bg-dark-primary px-3 py-1.5 rounded-full text-white text-sm"
                    (click)="sendEditedMessage(chat)"
                  >
                    Send
                  </button>
                </div>
              </div>
              } @if (chat.typeMessage == 'complex' && chat.role == 'user' &&
              chat.content) {
              <app-complex-message
                [type]="chat.content.complex_type"
                [content]="chat.content"
                (onActionClick)="replyMessage($event)"
              ></app-complex-message>
              } @if (chat.typeMessage == 'text' || (chat.role == 'user' &&
              !(isEditingMessage() && chat.id === editingMessageId())) &&
              chat.content && chat.typeMessage !== 'complex') {
              <div
                class="flex-grow w-full ml-2 px-3 py-1 !rounded-xl overflow-x-auto"
                [ngClass]="{
                  'bg-gray-100 dark:bg-dark-secondary max-w-full':
                    chat.role === 'assistant' || chat.role === 'human_operator',
                  'bg-light-primary2 dark:bg-dark-primary2':
                    chat.role === 'user'
                }"
                [dxTooltip]="(chat.created_at | date : 'HH:mm:ss') ?? ''"
                [dxTooltipPosition]="'below'"
              >
                <div
                  class="overflow-wrap whitespace-pre-wrap"
                  [ngClass]="{
                    'text-black dark:text-dark-text':
                      chat.role === 'assistant' ||
                      chat.role === 'human_operator',
                    'text-white': chat.role === 'user'
                  }"
                >
                  <div
                    class="!text-sm !leading-5.5"
                    [innerHTML]="chat.content | safeHtml"
                  ></div>

                  @if (last && isLoadingImage()) {
                  <div class="image-loader"></div>
                  }
                </div>

                @if (chat.showSource && chat.listTextSources &&
                chat.listTextSources.length > 0) {
                <br />
                <div
                  class="grid grid-cols-1 gap-4 mb-2 p-3 border border-gray-200 dark:border-dark-borderLine bg-gray-50 dark:bg-dark-secondaryBackground rounded-2xl"
                >
                  @for (source of chat.listTextSources; track source.source) {
                  <a
                    class="col-span-1 flex items-center justify-between space-x-1 px-2 py-1 bg-[#d4c6ff] dark:bg-[#4a3a8c] text-black dark:text-dark-text rounded-xl cursor-pointer hover:bg-[#f0ecff] dark:hover:bg-[#5a4a9c] hover:text-light-primary dark:hover:text-dark-primary hover:underline"
                    [dxTooltip]="source ? source?.source : ''"
                    [href]="source ? source?.source : '#'"
                    target="_blank"
                    dxTooltipPosition="below"
                  >
                    <div class="flex-grow truncate">{{ source?.title }}</div>
                    <div class="flex items-center justify-center">
                      <ng-icon
                        name="faSolidShareFromSquare"
                        class="text-right"
                      ></ng-icon>
                    </div>
                  </a>
                  }
                </div>
                }
              </div>
              }
            </div>

            @if (chat.typeMessage == 'grid') {
            <app-image-grid
              class="ml-[7px]"
              style="--col: 1"
              [imageSrcs]="chat.listImages"
            ></app-image-grid>
            } @if (chat.typeMessage == 'gallery') {
            <app-image-gallery
              class="ml-[7px]"
              style="
                --max-width: 212px;
                --img-width: 64px;
                --img-height: 64px;
                --gap: 6px;
                --label-space: 6px;
              "
              [imageSrcs]="chat.listImages"
            ></app-image-gallery>
            } @if (chat.typeMessage == 'logging') {
            <div
              class="w-full px-4"
              [dxTooltip]="(chat.created_at | date : 'HH:mm:ss') ?? ''"
              [dxTooltipPosition]="'below'"
            >
              <div
                class="flex-grow w-full overflow-wrap whitespace-pre-wrap !text-sm leading-5 text-left text-gray-500 dark:text-gray-400"
                style="font-size: 12px"
                [innerHTML]="chat.content"
              ></div>
            </div>
            } @if (chat.typeMessage == 'error') {
            <div
              class="w-full bg-[#FBEFEB] dark:bg-[#3a2a2a] p-3 rounded-lg grid grid-cols-6 gap-1 items-center border-2 border-[#EFD0C7] dark:border-[#6a3a3a]"
            >
              <ng-icon
                name="heroExclamationTriangleSolid"
                class="col-span-1 text-[#F55758] text-3xl"
              ></ng-icon>
              <div
                class="col-span-5 flex-grow w-full overflow-wrap whitespace-pre-wrap !text-sm leading-5 text-left text-light-text dark:text-dark-text"
                style="font-size: 12px"
                [innerHTML]="chat.content"
              ></div>
            </div>
            }
          </div>
          } @if (isLoadMessageAI()) {
          <div class="flex items-center mb-4">
            <div
              class="typing-animation bg-light-secondary-background dark:bg-dark-secondary-background p-2 rounded-lg mb-2 relative"
            >
              <div class="dot dark:!bg-dark-text"></div>
              <div class="dot dark:!bg-dark-text"></div>
              <div class="dot dark:!bg-dark-text"></div>
            </div>
          </div>
          }
        </div>
      </ng-scrollbar>
    </div>
  </div>

  <div
    class="fixed bottom-[72px] left-0 right-0 z-10 p-4 rounded-b-2xl flex items-center"
  >
    <dx-form-field class="w-full flex items-center" [subscriptHidden]="true">
      <textarea
        id="inboxChat"
        dxInput
        rows="1"
        class="resize-none"
        [placeholder]="placeholder()"
        [ngModel]="message"
        (ngModelChange)="message = $event; updateTextAreaHeight()"
        (keydown)="onKeyDown($event)"
      ></textarea>
      <app-svg-icon
        dxSuffix
        type="icSend"
        class="w-6 h-6 !text-primary dark:!text-dark-primary mr-3 cursor-pointer z-2"
        [ngClass]="{
          '!text-neutral-content dark:!text-dark-neutral-content':
            isSendingMessage()
        }"
        (click)="!isSendingMessage() && replyMessage(); updateTextAreaHeight()"
      >
      </app-svg-icon>
    </dx-form-field>
  </div>
</div>
} @else {
<div
  class="relative h-full w-full p-6 bg-base-200 dark:bg-dark-base-200 border border-[#D2D6DD] dark:border-[#292E39] rounded-2xl flex flex-col space-y-5"
>
  <header class="flex items-center justify-between">
    <div class="flex items-center">
      @if (header() && header().trim() !== '') { @if (logoUrl()) {
      <div class="w-8 h-8 flex mr-2">
        <img
          class="rounded-full object-cover"
          [src]="logoUrl()"
          alt="This is logo header"
        />
      </div>
      }
      <div class="text-xl font-bold text-light-text dark:text-dark-text">
        {{ header() }}
      </div>
      } @else {
      <div class="text-xl font-bold text-light-text dark:text-dark-text">
        Preview
      </div>
      <ng-icon
        class="ml-1 text-xl text-light-text dark:text-light-text"
        name="heroInformationCircle"
        dxTooltip="Edit header in settings"
        dxTooltipPosition="below"
      ></ng-icon>
      }
    </div>

    @if (!isReloadChat()) {
    <app-custom-icon
      iconName="heroArrowPath"
      [size]="22"
      class="text-2xl text-light-text dark:text-dark-text"
      (click)="reloadChat(true)"
      dxTooltip="Reset new conversation"
      dxTooltipPosition="below"
    ></app-custom-icon>
    <!--<ng-icon class="text-2xl text-light-text dark:text-dark-text" name="heroArrowPath" (click)="reloadChat(true)"
          dxTooltip="Reset new conversation" tooltipPosition="bottom"></ng-icon>-->
    } @else {
    <div role="status">
      <svg
        aria-hidden="true"
        class="inline w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-light-primary"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
          fill="currentColor"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          fill="currentFill"
        />
      </svg>
      <span class="sr-only">Loading...</span>
    </div>
    }
  </header>

  <div class="h-full flex flex-col overflow-hidden">
    <div class="overflow-y-auto flex-1">
      <ng-scrollbar
        class="scroll-custom"
        #scrollRef
        track="vertical"
        appearance="compact"
        visibility="hover"
        (vmScrolled)="onScroll()"
      >
        <div class="space-y-4 mb-16">
          @for (chat of listChatMessage(); track trackByChat($index, chat); let
          last = $last) {
          <div
            class="flex items-start"
            [ngClass]="{
              'justify-start':
                chat.role === 'assistant' || chat.role === 'human_operator',
              'justify-end': chat.role === 'user'
            }"
          >
            @if ((chat.role === 'assistant' || chat.role === 'human_operator')
            && chat.typeMessage !== 'error' && chat.typeMessage !== 'logging' &&
            chat.content) {
            <div>
              <div class="w-7 h-7 rounded-full relative">
                <img
                  src="./assets/img/logo-chat.png"
                  alt="This is logo"
                  class="w-7 h-7"
                />
              </div>
            </div>
            }

            <div
              class="flex items-center max-w-[80%]"
              [class.w-full]="
                isEditingMessage() &&
                editingMessageId() === chat.id &&
                chat.role == 'user'
              "
            >
              @if (!isEditingMessage() && !isSendingMessage() && chat.role ==
              'user' && chat.typeMessage !== 'complex') {
              <ng-icon
                name="heroPencilSquareMini"
                size="20"
                class="flex-shrink-0 !text-yellow-500 cursor-pointer"
                (click)="editMessage(chat)"
                dxTooltip="Edit message"
                tooltipPosition="bottom"
              ></ng-icon>
              } @if (isEditingMessage() && editingMessageId() === chat.id &&
              chat.role == 'user' && chat.typeMessage !== 'complex') {
              <div
                class="w-full p-2.5 flex flex-col space-y-3 bg-gray-200 dark:bg-dark-secondary rounded-xl"
              >
                <textarea
                  class="w-full p-2 rounded-lg placeholder-gray-400 dark:placeholder-gray-200 text-sm border border-gray-300 rounded-lg focus:outline-none resize-none bg-gray-50 dark:bg-dark-background text-light-text dark:text-dark-text focus:ring-light-primary focus:border-light-primary dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-light-primary dark:focus:border-light-primary"
                  [(ngModel)]="editedMessage"
                  [ngModelOptions]="{ standalone: true }"
                  rows="2"
                  (input)="adjustHeight($event)"
                >
                </textarea>
                <div class="w-full flex justify-end items-center space-x-3">
                  <button
                    class="bg-gray-500 px-3 py-1.5 rounded-full text-white text-sm"
                    (click)="cancelEditMessage(chat)"
                  >
                    Cancel
                  </button>
                  <button
                    class="bg-light-primary dark:bg-dark-primary px-3 py-1.5 rounded-full text-white text-sm"
                    (click)="sendEditedMessage(chat)"
                  >
                    Send
                  </button>
                </div>
              </div>
              } @if (chat.typeMessage == 'complex' && chat.role == 'user' &&
              chat.content) {
              <app-complex-message
                [type]="chat.content.complex_type"
                [content]="chat.content"
                (onActionClick)="replyMessage($event)"
              ></app-complex-message>
              } @if (chat.typeMessage == 'text' || (chat.role == 'user' &&
              !(isEditingMessage() && chat.id === editingMessageId())) &&
              chat.content && chat.typeMessage !== 'complex') {
              <div
                class="flex-grow w-full ml-2 px-3 py-1 !rounded-xl overflow-x-auto"
                [ngClass]="{
                  'bg-gray-100 dark:bg-dark-secondary max-w-full':
                    chat.role === 'assistant' || chat.role === 'human_operator',
                  'bg-light-primary2 dark:bg-dark-primary2':
                    chat.role === 'user'
                }"
                [dxTooltip]="(chat.created_at | date : 'HH:mm:ss') ?? ''"
                [dxTooltipPosition]="'below'"
              >
                <div
                  class="overflow-wrap whitespace-pre-wrap"
                  [ngClass]="{
                    'text-black dark:text-dark-text':
                      chat.role === 'assistant' ||
                      chat.role === 'human_operator',
                    'text-white': chat.role === 'user'
                  }"
                >
                  <div
                    class="!text-sm !leading-5.5"
                    [innerHTML]="chat.content | safeHtml"
                  ></div>

                  @if (last && isLoadingImage()) {
                  <div class="image-loader"></div>
                  }
                </div>

                @if (chat.showSource && chat.listTextSources &&
                chat.listTextSources.length > 0) {
                <br />
                <div
                  class="grid grid-cols-1 gap-4 mb-2 p-3 border border-gray-200 dark:border-dark-borderLine bg-gray-50 dark:bg-dark-secondaryBackground rounded-2xl"
                >
                  @for (source of chat.listTextSources; track source.source) {
                  <a
                    class="col-span-1 flex items-center justify-between space-x-1 px-2 py-1 bg-[#d4c6ff] dark:bg-[#4a3a8c] text-black dark:text-dark-text rounded-xl cursor-pointer hover:bg-[#f0ecff] dark:hover:bg-[#5a4a9c] hover:text-light-primary dark:hover:text-dark-primary hover:underline"
                    [dxTooltip]="source ? source?.source : ''"
                    [href]="source ? source?.source : '#'"
                    target="_blank"
                    dxTooltipPosition="below"
                  >
                    <div class="flex-grow truncate">{{ source?.title }}</div>
                    <div class="flex items-center justify-center">
                      <ng-icon
                        name="faSolidShareFromSquare"
                        class="text-right"
                      ></ng-icon>
                    </div>
                  </a>
                  }
                </div>
                }
              </div>
              }
            </div>

            @if (chat.typeMessage == 'grid') {
            <app-image-grid
              class="ml-[7px]"
              style="--col: 1"
              [imageSrcs]="chat.listImages"
            ></app-image-grid>
            } @if (chat.typeMessage == 'gallery') {
            <app-image-gallery
              class="ml-[7px]"
              style="
                --max-width: 212px;
                --img-width: 64px;
                --img-height: 64px;
                --gap: 6px;
                --label-space: 6px;
              "
              [imageSrcs]="chat.listImages"
            ></app-image-gallery>
            } @if (chat.typeMessage == 'logging') {
            <div
              class="w-full px-4"
              [dxTooltip]="(chat.created_at | date : 'HH:mm:ss') ?? ''"
              [dxTooltipPosition]="'below'"
            >
              <div
                class="flex-grow w-full overflow-wrap whitespace-pre-wrap !text-sm leading-5 text-left text-gray-500 dark:text-gray-400"
                style="font-size: 12px"
                [innerHTML]="chat.content"
              ></div>
            </div>
            } @if (chat.typeMessage == 'error') {
            <div
              class="w-full bg-[#FBEFEB] dark:bg-[#3a2a2a] p-3 rounded-lg grid grid-cols-6 gap-1 items-center border-2 border-[#EFD0C7] dark:border-[#6a3a3a]"
            >
              <ng-icon
                name="heroExclamationTriangleSolid"
                class="col-span-1 text-[#F55758] text-3xl"
              ></ng-icon>
              <div
                class="col-span-5 flex-grow w-full overflow-wrap whitespace-pre-wrap !text-sm leading-5 text-left text-light-text dark:text-dark-text"
                style="font-size: 12px"
                [innerHTML]="chat.content"
              ></div>
            </div>
            }
          </div>
          } @if (isLoadMessageAI()) {
          <div class="flex items-center mb-4">
            <div
              class="typing-animation bg-light-secondary-background dark:bg-dark-secondary-background p-2 rounded-lg mb-2 relative"
            >
              <div class="dot dark:!bg-dark-text"></div>
              <div class="dot dark:!bg-dark-text"></div>
              <div class="dot dark:!bg-dark-text"></div>
            </div>
          </div>
          }
        </div>
      </ng-scrollbar>
    </div>
  </div>

  <div
    class="absolute bottom-0 left-0 right-0 p-6 rounded-b-2xl flex items-center"
  >
    <dx-form-field class="w-full flex items-center" [subscriptHidden]="true">
      <textarea
        id="inboxChat"
        dxInput
        rows="1"
        class="resize-none"
        [placeholder]="placeholder()"
        [ngModel]="message"
        (ngModelChange)="message = $event; updateTextAreaHeight()"
        (keydown)="onKeyDown($event)"
      ></textarea>
      <app-svg-icon
        dxSuffix
        type="icSend"
        class="w-6 h-6 !text-primary dark:!text-dark-primary mr-3 cursor-pointer z-2"
        [ngClass]="{
          '!text-neutral-content dark:!text-dark-neutral-content':
            isSendingMessage()
        }"
        (click)="!isSendingMessage() && replyMessage(); updateTextAreaHeight()"
      >
      </app-svg-icon>
    </dx-form-field>
  </div>
</div>
}
