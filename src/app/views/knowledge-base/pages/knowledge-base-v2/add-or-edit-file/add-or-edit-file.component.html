<div class="h-full relative flex flex-col rounded-3xl">
  <div class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100">
    <div class="flex flex-grow truncate items-center text-2xl font-bold text-base-content dark:text-dark-base-content">
      {{ data.isView ? data.file.name : 'Rename File' }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="flex flex-shrink-0 w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>

  <div class="flex-1 overflow-y-auto mt-18 mb-20 px-6 pt-6 pb-[3px] flex flex-col space-x-4">
    @if (data.isView && data.file) {
        <div class="space-y-4 mb-6">
          <div class="flex flex-col space-y-4">
            <div class="grid grid-cols-2 gap-x-4">
              <div class="flex items-center space-x-2">
                <app-svg-icon type="icCalendar" class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
                <dx-label class="text-sm font-medium" [style.--dx-form-field-label-offset-y]="0">Created</dx-label>
              </div>
              <div class="text-sm text-light-text dark:text-dark-text">
                {{ data.file.created_at | date:'d MMM, y|  HH:mm:ss' }}
              </div>
            </div>
            <div class="grid grid-cols-2 gap-x-4">
              <div class="flex items-center space-x-2">
                <app-svg-icon type="icUpdate" class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
                <dx-label class="text-sm font-medium" [style.--dx-form-field-label-offset-y]="0">Updated</dx-label>
              </div>
              <span class="text-sm text-light-text dark:text-dark-text">
                {{ data.file.updated_at | date:'d MMM, y|  HH:mm:ss' }}
              </span>
            </div>
            <div class="grid grid-cols-2 gap-x-4">
              <div class="flex items-center space-x-2">
                <app-svg-icon type="icActivity" class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
                <dx-label class="text-sm font-medium" [style.--dx-form-field-label-offset-y]="0">Status</dx-label>
              </div>
              <span class="px-2 py-1 rounded-full text-xs w-fit"
                    [class]="data.file.status === 'COMPLETED' ? 'bg-success dark:bg-dark-success' :
                          data.file.status === 'IN_PROGRESS' ? 'bg-info dark:bg-dark-info' :
                          'bg-error dark:bg-dark-error'">
              {{ data.file.status || 'Unknown' }}
              </span>
            </div>
          </div>
          @if (data.file.url || data.file.file_path) {
            <div class="grid grid-cols-2 gap-x-4">
              <div class="flex items-center space-x-2">
                <app-svg-icon type="icLinkPrefix" class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
                <dx-label class="text-sm font-medium" [style.--dx-form-field-label-offset-y]="0">File Path</dx-label>
              </div>
              <div class="flex items-center space-x-2">
                <a
                  [href]="data.file.url ? data.file.url : data.file.file_path"
                  class="text-light-primary dark:text-dark-primary truncate max-w-[40dvh]"
                  target="_blank"
                >{{ data.file.url ? data.file.url : data.file.file_path }}</a>
                <app-svg-icon type="icCopy" class="flex flex-shrink-0 w-6 h-6 !text-primary dark:!text-dark-primary"
                              (click)="copyToClipboard(data.file.url ? data.file.url : data.file.file_path)">
                </app-svg-icon>
              </div>
            </div>
          }

          <!--@if (data.file.description) {
            <div>
              <label class="block text-sm font-medium mb-1">Description</label>
              <p class="text-light-text dark:text-dark-text">{{ data.file.description }}</p>
            </div>
          }-->
        </div>
    } @else {
    <dx-form-field class="w-full" id="name">
      <dx-label class="text-sm">Name<span class="text-red-500">*</span></dx-label>
      <input
        dxInput
        [ngModel]="fileName()"
        (ngModelChange)="fileName.set($event)"
        type="text"
        placeholder="Enter new file name"
        (keydown.enter)="saveRenameFile()"
      />
    </dx-form-field>
    }
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Cancel</button>
    @if (data.isView) {
      <button dxButton="filled"
              (click)="viewContentFile(data.file)">
        View Content
      </button>

    } @else {
    <button
      dxLoadingButton="filled"
      [loading]="isEditing()"
      [disabled]="this.fileRename.old_name === this.fileName() || !this.fileName()"
      (click)="saveRenameFile()"
    >
      Rename
    </button>
    }
  </div>
</div>


<!--
<ng-template #viewFileInfoDialog let-data>
  <div
    class="flex flex-col h-full bg-light-secondary-background dark:bg-dark-secondary-background text-light-text dark:text-dark-text">
    <div
      class="header border-b border-light-border dark:border-dark-border pt-6 px-6 flex justify-between items-center pb-3">
      <div class="text-2xl font-bold card-title capitalize">
        File Information
      </div>
      <div class="hover:cursor-pointer" (click)="closeDialog()">
        <ng-icon name="heroXMark" class="text-2xl text-light-text dark:text-dark-text"></ng-icon>
      </div>
    </div>

    <div class="w-full px-6 pt-6 content overflow-auto max-h-[75vh]">

    </div>

    <div class="footer px-6 py-4 border-t border-light-border dark:border-dark-border flex justify-end space-x-3">
      <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              (click)="viewContentFile(data.file)">
        View Content
      </button>
      <button
        class="px-4 py-2 border border-light-border dark:border-dark-border rounded-lg hover:bg-light-hover dark:hover:bg-dark-hover"
        (click)="closeDialog()">
        Close
      </button>
    </div>
  </div>
</ng-template>

-->
