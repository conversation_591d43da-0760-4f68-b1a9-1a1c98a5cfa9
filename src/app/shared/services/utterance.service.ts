import {inject, Injectable} from '@angular/core';
import { environment } from '@env/environment';
import {HttpClient} from '@angular/common/http';
import {IUtterance, IUtteranceFilter} from '@shared/models';
import {Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UtteranceService {

  private url = `${environment.FLOW_URL}/intent/utterance`;

  private httpClient: HttpClient = inject<HttpClient>(HttpClient);

  getListUtterance(body: IUtteranceFilter): Observable<Array<IUtterance>> {
    return this.httpClient.post<Array<IUtterance>>(`${this.url}/list`, body);
  }
}
