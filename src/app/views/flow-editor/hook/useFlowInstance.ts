// @ts-nocheck
import { nodeApi } from '@flow-editor/api';
import {
  BuildFlowState,
  FlowInstanceContext,
  FlowInstanceState,
  LayoutState,
  NodeDefine,
} from '@flow-editor/model';
import {
  useBuildFlowState,
  useFlowInstanceState,
  useLayoutState,
} from '@flow-editor/store';
import { getUniqueNodeId } from '@flow-editor/utils/flow';
import { cloneDeep } from 'lodash';

export const useFlowInstance = (): FlowInstanceContext => {
  const { flowInstance } = useFlowInstanceState<FlowInstanceState>(
    (state) => state
  );
  const { flow, setDirty } = useBuildFlowState<BuildFlowState>(
    (state) => state
  );
  const { setTheme } = useLayoutState<LayoutState>((state) => state);

  const duplicateNode = (nodeId: string) => {
    if (!flow?.id) return;
    const nodes = flowInstance.getNodes();
    const originalNode = nodes.find((n) => n.id === nodeId);
    if (originalNode) {
      const newNodeId = getUniqueNodeId(originalNode.data, nodes, flow.id);
      const clonedNode = cloneDeep(originalNode);
      const duplicatedNode = {
        ...clonedNode,
        id: newNodeId,
        position: {
          x: clonedNode.position.x + 100,
          y: clonedNode.position.y + 50,
        },
        positionAbsolute: {
          x: clonedNode.positionAbsolute.x + 100,
          y: clonedNode.positionAbsolute.y + 50,
        },
        data: {
          ...clonedNode.data,
          id: newNodeId,
        },
        selected: false,
      };

      originalNode.selected = false;
      flowInstance.setNodes([...nodes, duplicatedNode]);
      setDirty();
    }
  };

  const convertNode = async (nodeId: string) => {
    if (!flow?.id) return;
    const nodes = flowInstance.getNodes();
    const originalNode = nodes.find((n) => n.id === nodeId);
    if (originalNode) {
      const clonedNode = cloneDeep(originalNode);
      let newNodeData;
      const fetchNodes = async () => {
        const res: Array<NodeDefine> = await nodeApi.getListNode(
          flow.trigger_type
        );
        const parsedRes = res.map((node) => {
          return {
            ...node,
            data: JSON.parse(node.data),
          };
        });
        if (clonedNode.type === 'question') {
          // Find the node with data.node_type === "text"
          newNodeData = parsedRes.find(
            (node) => node.data.node_type === 'text'
          );
        } else {
          // Find the node with data.node_type === "question"
          newNodeData = parsedRes.find(
            (node) => node.data.node_type === 'question'
          );
        }
      };
      await fetchNodes();
      const newNodeId = getUniqueNodeId(newNodeData.data, nodes, flow.id);
      const convertNode = {
        ...clonedNode,
        id: newNodeId,
        position: {
          x: clonedNode.position.x + 100,
          y: clonedNode.position.y + 50,
        },
        positionAbsolute: {
          x: clonedNode.positionAbsolute.x + 100,
          y: clonedNode.positionAbsolute.y + 50,
        },
        data: {
          ...newNodeData.data,
          id: newNodeId,
          messages: clonedNode.data?.messages,
        },
        type: clonedNode.type === 'question' ? 'text' : 'question',
        selected: false,
      };

      originalNode.selected = false;

      flowInstance.setNodes([...nodes, convertNode]);
      setDirty();
    }
  };

  const deleteNode = (nodeId: string) => {
    if (nodeId) {
      deleteConnectedInput(nodeId, 'node');
      flowInstance.setNodes(
        flowInstance.getNodes().filter((n) => n.id !== nodeId)
      );
      flowInstance.setNodes(
        flowInstance.getNodes().filter((n) => n.id !== nodeId)
      );
      flowInstance.setEdges(
        flowInstance
          .getEdges()
          .filter((e) => e.source !== nodeId && e.target !== nodeId)
      );
      setDirty();
    }
  };

  const deleteNodes = (nodeIds: string[]) => {
    if (nodeIds && nodeIds.length) {
      nodeIds.forEach((nodeId) => {
        deleteConnectedInput(nodeId, 'node');
      });
      flowInstance.setNodes(
        flowInstance.getNodes().filter((n) => !nodeIds.includes(n.id))
      );
      flowInstance.setEdges(
        flowInstance
          .getEdges()
          .filter(
            (e) => !nodeIds.includes(e.source) && !nodeIds.includes(e.target)
          )
      );
      setDirty();
    }
  };

  const deleteEdge = (edgeId: string) => {
    deleteConnectedInput(edgeId, 'edge');
    flowInstance.setEdges(
      flowInstance.getEdges().filter((edge) => edge.id !== edgeId)
    );
    setDirty();
  };

  const deleteConnectedInput = (id, type) => {
    const connectedEdges =
      type === 'node'
        ? flowInstance.getEdges().filter((edge) => edge.source === id)
        : flowInstance.getEdges().filter((edge) => edge.id === id);

    for (const edge of connectedEdges) {
      const targetNodeId = edge.target;
      const targetInput = edge.targetHandle.split('-')[2];

      flowInstance.setNodes((nds) =>
        nds.map((node) => {
          if (node.id === targetNodeId) {
            node.data = {
              ...node.data,
            };
          }
          return node;
        })
      );
    }
  };

  const resetStyle = () => {
    flowInstance.setNodes((nds) =>
      nds.map((node) => {
        node.selected = false;
        node.data.selected = false;
        node.style = {
          ...node.style,
          opacity: 1,
        };
        return node;
      })
    );
    flowInstance.setEdges((eds) =>
      eds.map((edge) => {
        edge.animated = false;
        if (!edge.data?.isGoToBlock) {
          edge.style = {
            ...edge.style,
            stroke: setTheme === 'dark' ? 'white' : '#94a2b8',
            strokeWidth: 1,
            strokeOpacity: 1,
            opacity: 1,
          };
        } else {
          edge.style = {
            ...edge.style,
            opacity: 0,
          };
        }
        return edge;
      })
    );
  };

  const unSelectNode = (nodeId: string) => {
    flowInstance.setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          node.selected = false;
          node.data.selected = false;
        } else {
          node.style = {
            ...node.style,
            opacity: 0.3,
          };
        }
        return node;
      })
    );
    flowInstance.setEdges((eds) =>
      eds.map((edge) => {
        if (!(edge.target === nodeId || edge.source === nodeId)) {
          if (!edge.data?.isGoToBlock) {
            edge.style = {
              ...edge.style,
              opacity: 0.3,
            };
          } else {
            edge.style = {
              ...edge.style,
              opacity: 0,
            };
          }
        } else {
          if (!edge.data?.isGoToBlock) {
            edge.animated = true;
            edge.style = {
              ...edge.style,
              stroke: flowInstance.getNodes().find((node) => node.id === nodeId)
                .data.node_color,
              strokeWidth: 2,
              strokeOpacity: 1,
              opacity: 1,
            };
          } else {
            edge.animated = false;
            edge.style = {
              ...edge.style,
              opacity: 0,
            };
          }
        }
        return edge;
      })
    );
  };

  return {
    duplicateNode,
    deleteNode,
    deleteNodes,
    deleteEdge,
    convertNode,
    unSelectNode,
    resetStyle,
  };
};
